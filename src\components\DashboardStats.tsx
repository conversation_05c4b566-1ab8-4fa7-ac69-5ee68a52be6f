import React from 'react';
import { Building2, Package, Clock, CheckCircle, XCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SupplierData } from './SupplierDataTable';

interface DashboardStatsProps {
  data: SupplierData[];
}

export const DashboardStats: React.FC<DashboardStatsProps> = ({ data }) => {
  const stats = {
    totalSuppliers: new Set(data.map(item => item.supplier)).size,
    totalParts: data.length,
    completedData: data.filter(item => {
      const status = item.dataCollectionStatus;
      const avgCompletion = (status.process + status.energyConsumption + status.specialProcessMaterial + status.waste + status.transportation) / 5;
      return avgCompletion >= 90;
    }).length,
    pendingData: data.filter(item => {
      const status = item.dataCollectionStatus;
      const avgCompletion = (status.process + status.energyConsumption + status.specialProcessMaterial + status.waste + status.transportation) / 5;
      return avgCompletion >= 10 && avgCompletion < 90;
    }).length,
    notStartedData: data.filter(item => {
      const status = item.dataCollectionStatus;
      const avgCompletion = (status.process + status.energyConsumption + status.specialProcessMaterial + status.waste + status.transportation) / 5;
      return avgCompletion < 10;
    }).length
  };

  const completionRate = Math.round((stats.completedData / stats.totalParts) * 100);

  return (
    <div className="w-full">
      {/* Title Section */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold tracking-tight text-foreground mb-2">
          Supplier LCA Data
        </h1>
        <p className="text-lg text-muted-foreground">
          Track and manage supplier lifecycle assessment information
        </p>
      </div>

      {/* Stats Grid - Full Width */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 lg:gap-6">
        <Card className="border-l-4 border-l-primary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Suppliers</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSuppliers}</div>
            <p className="text-xs text-muted-foreground">
              Active supplier relationships
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-secondary">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Parts</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalParts}</div>
            <p className="text-xs text-muted-foreground">
              Parts tracked in system
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed LCA</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedData}</div>
            <p className="text-xs text-muted-foreground">
              {completionRate}% completion rate
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Action</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingData}</div>
            <p className="text-xs text-muted-foreground">
              Requires data collection
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Not Started</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.notStartedData}</div>
            <p className="text-xs text-muted-foreground">
              No data collection yet
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};