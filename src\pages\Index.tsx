import { useToast } from '@/hooks/use-toast';
import { SupplierDataTable } from '@/components/SupplierDataTable';
import { DashboardStats } from '@/components/DashboardStats';
import { sampleSupplierData } from '@/data/sampleSupplierData';

const Index = () => {
  const { toast } = useToast();

  const handleAddNew = () => {
    toast({
      title: "Add New Supplier",
      description: "New supplier form would open here",
    });
  };

  const handleExport = () => {
    toast({
      title: "Export Data",
      description: "Data exported successfully to CSV",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted">
      {/* Hero Section */}
      {/* <div className="relative overflow-hidden">
        <div 
          className="h-64 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${heroImage})` }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-secondary/80" />
          <div className="relative z-10 container mx-auto px-6 py-16">
            <div className="max-w-3xl">
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                Supplier LCA Tracker
              </h1>
              <p className="text-xl text-white/90 mb-6">
                Comprehensive lifecycle assessment data management for automotive suppliers and parts
              </p>
              <div className="flex flex-wrap gap-4 text-white/80 text-sm">
                <span className="flex items-center gap-2">
                  ✓ Real-time data tracking
                </span>
                <span className="flex items-center gap-2">
                  ✓ IMDS integration
                </span>
                <span className="flex items-center gap-2">
                  ✓ Compliance monitoring
                </span>
              </div>
            </div>
          </div>
        </div>
      </div> */}

      {/* Main Content */}
      <div className="space-y-8">
        {/* Dashboard Stats - Full Width */}
        <div className="w-full px-4">
          <DashboardStats data={sampleSupplierData} />
        </div>

        {/* Supplier Data Table - Full Width with minimal padding */}
        <div className="w-full px-4">
          <SupplierDataTable
            data={sampleSupplierData}
            onAddNew={handleAddNew}
            onExport={handleExport}
          />
        </div>
      </div>
    </div>
  );
};

export default Index;
