import { SupplierData } from '@/components/SupplierDataTable';

export const sampleSupplierData: SupplierData[] = [
  {
    id: '1',
    supplier: 'Bosch Automotive',
    vendorCode: 'BSH001',
    partNumber: 'BOS-12345-67',
    description: 'Electronic Control Unit for Engine Management System',
    imdsId: 'IMDS-78901',
    lastUpdated: new Date('2024-01-15'),
    dataCollectionStatus: {
      process: 100,
      energyConsumption: 95,
      specialProcessMaterial: 100,
      waste: 90,
      transportation: 85
    }
  },
  {
    id: '2',
    supplier: 'Continental AG',
    vendorCode: 'CON002',
    partNumber: 'CNT-98765-43',
    description: 'Brake System Controller with ABS functionality',
    imdsId: 'IMDS-45612',
    lastUpdated: new Date('2024-01-12'),
    dataCollectionStatus: {
      process: 85,
      energyConsumption: 70,
      specialProcessMaterial: 60,
      waste: 75,
      transportation: 80
    }
  },
  {
    id: '3',
    supplier: 'Magna International',
    vendorCode: 'MAG003',
    partNumber: 'MAG-55555-21',
    description: 'Transmission Housing Assembly - Aluminum Alloy',
    imdsId: '',
    lastUpdated: new Date('2023-12-20'),
    dataCollectionStatus: {
      process: 40,
      energyConsumption: 30,
      specialProcessMaterial: 0,
      waste: 25,
      transportation: 35
    }
  },
  {
    id: '4',
    supplier: 'Denso Corporation',
    vendorCode: 'DNS004',
    partNumber: 'DNS-77777-89',
    description: 'Air Conditioning Compressor Unit',
    imdsId: 'IMDS-98765',
    lastUpdated: new Date('2024-01-18'),
    dataCollectionStatus: {
      process: 100,
      energyConsumption: 100,
      specialProcessMaterial: 95,
      waste: 100,
      transportation: 95
    }
  },
  {
    id: '5',
    supplier: 'ZF Friedrichshafen',
    vendorCode: 'ZFF005',
    partNumber: 'ZFF-33333-44',
    description: 'Steering System Component - Electric Power Steering',
    imdsId: 'IMDS-11223',
    lastUpdated: new Date('2024-01-10'),
    dataCollectionStatus: {
      process: 75,
      energyConsumption: 65,
      specialProcessMaterial: 80,
      waste: 70,
      transportation: 60
    }
  },
  {
    id: '6',
    supplier: 'Aisin Corporation',
    vendorCode: 'AIS006',
    partNumber: 'AIS-66666-78',
    description: 'Automatic Transmission Valve Body',
    imdsId: '',
    lastUpdated: new Date('2023-11-15'),
    dataCollectionStatus: {
      process: 0,
      energyConsumption: 0,
      specialProcessMaterial: 0,
      waste: 0,
      transportation: 0
    }
  },
  {
    id: '7',
    supplier: 'Valeo Group',
    vendorCode: 'VAL007',
    partNumber: 'VAL-88888-90',
    description: 'LED Headlight Assembly with DRL',
    imdsId: 'IMDS-55566',
    lastUpdated: new Date('2024-01-14'),
    dataCollectionStatus: {
      process: 100,
      energyConsumption: 90,
      specialProcessMaterial: 100,
      waste: 95,
      transportation: 90
    }
  },
  {
    id: '8',
    supplier: 'Schaeffler Group',
    vendorCode: 'SCH008',
    partNumber: 'SCH-99999-01',
    description: 'Timing Chain Tensioner System',
    imdsId: 'IMDS-77788',
    lastUpdated: new Date('2024-01-08'),
    dataCollectionStatus: {
      process: 80,
      energyConsumption: 75,
      specialProcessMaterial: 70,
      waste: 65,
      transportation: 85
    }
  },
  {
    id: '9',
    supplier: 'Mahle GmbH',
    vendorCode: 'MAH009',
    partNumber: 'MAH-11111-23',
    description: 'Engine Oil Filter with Bypass Valve',
    imdsId: '',
    lastUpdated: new Date('2023-12-05'),
    dataCollectionStatus: {
      process: 30,
      energyConsumption: 20,
      specialProcessMaterial: 15,
      waste: 25,
      transportation: 10
    }
  },
  {
    id: '10',
    supplier: 'BorgWarner Inc.',
    vendorCode: 'BWI010',
    partNumber: 'BWI-44444-56',
    description: 'Turbocharger Assembly - Variable Geometry',
    imdsId: 'IMDS-33344',
    lastUpdated: new Date('2024-01-16'),
    dataCollectionStatus: {
      process: 100,
      energyConsumption: 95,
      specialProcessMaterial: 100,
      waste: 100,
      transportation: 90
    }
  },
  {
    id: '11',
    supplier: 'Aptiv PLC',
    vendorCode: 'APT011',
    partNumber: 'APT-22222-34',
    description: 'Vehicle Connectivity Module 5G',
    imdsId: 'IMDS-99900',
    lastUpdated: new Date('2024-01-11'),
    dataCollectionStatus: {
      process: 60,
      energyConsumption: 55,
      specialProcessMaterial: 70,
      waste: 65,
      transportation: 50
    }
  },
  {
    id: '12',
    supplier: 'Lear Corporation',
    vendorCode: 'LEA012',
    partNumber: 'LEA-55555-67',
    description: 'Premium Leather Seat Cover Assembly',
    imdsId: '',
    lastUpdated: new Date('2023-10-28'),
    dataCollectionStatus: {
      process: 0,
      energyConsumption: 0,
      specialProcessMaterial: 0,
      waste: 0,
      transportation: 0
    }
  }
];