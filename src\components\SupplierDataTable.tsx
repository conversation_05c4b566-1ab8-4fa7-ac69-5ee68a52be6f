import React, { useState, useMemo } from 'react';
import {
  Search,
  Download,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  ChevronDown,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

// Types for our supplier data
export interface DataCollectionCategory {
  process: number; // 0-100 percentage
  energyConsumption: number;
  specialProcessMaterial: number;
  waste: number;
  transportation: number;
}

export interface SupplierData {
  id: string;
  supplier: string;
  vendorCode: string;
  modelName: string;
  partNumber: string;
  description: string;
  imdsId: string;
  lastUpdated: Date;
  dataCollectionStatus: DataCollectionCategory;
}

interface SupplierDataTableProps {
  data: SupplierData[];
  onExport?: () => void;
}



// Component for displaying the data collection status in a single row
const DataCollectionStatusRow: React.FC<{ status: DataCollectionCategory }> = ({ status }) => {
  const categories = [
    { key: 'process', label: 'Process', value: status.process },
    { key: 'energyConsumption', label: 'Energy consumption', value: status.energyConsumption },
    { key: 'specialProcessMaterial', label: 'Social Process Material', value: status.specialProcessMaterial },
    { key: 'waste', label: 'Waste', value: status.waste },
    { key: 'transportation', label: 'Transportation', value: status.transportation },
  ];

  const getProgressColor = (value: number) => {
    if (value >= 90) return 'bg-green-500';
    if (value >= 50) return 'bg-yellow-500';
    if (value > 0) return 'bg-orange-500';
    return 'bg-gray-300';
  };

  return (
    <div className="w-full">
      {/* Progress Bars First */}
      <div className="grid grid-cols-5 gap-1 mb-2">
        {categories.map((category) => (
          <div key={category.key} className="px-2">
            <div className="w-full bg-gray-200 rounded h-2">
              <div
                className={`h-full rounded transition-all duration-300 ${getProgressColor(category.value)}`}
                style={{ width: `${category.value}%` }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Category Labels Below Progress Bars */}
      <div className="grid grid-cols-5 gap-1 mb-2">
        {categories.map((category) => (
          <div key={category.key} className="text-center text-xs font-medium text-gray-700 px-2">
            {category.label}
          </div>
        ))}
      </div>

      {/* Download Icons at Bottom */}
      <div className="grid grid-cols-5 gap-1">
        {categories.map((category) => (
          <div key={category.key} className="flex justify-center px-2">
            {category.value >= 90 ? (
              <Download className="h-4 w-4 text-gray-600 cursor-pointer hover:text-gray-800" />
            ) : (
              <div className="h-4 w-4" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export const SupplierDataTable: React.FC<SupplierDataTableProps> = ({
  data,
  onExport
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [supplierFilter, setSupplierFilter] = useState('');
  const [vendorCodeFilter, setVendorCodeFilter] = useState('');
  const [modelNameFilter, setModelNameFilter] = useState('');
  const [partNumberFilter, setPartNumberFilter] = useState('');
  const [imdsIdFilter, setImdsIdFilter] = useState('');
  const [dateSortDirection, setDateSortDirection] = useState<'asc' | 'desc' | null>(null);

  // Get unique values for dropdown filters
  const uniqueSuppliers = useMemo(() =>
    [...new Set(data.map(item => item.supplier))].sort(), [data]);
  const uniqueVendorCodes = useMemo(() =>
    [...new Set(data.map(item => item.vendorCode))].sort(), [data]);
  const uniqueModelNames = useMemo(() => {
    const allModels = data.flatMap(item =>
      item.modelName.split(',').map(model => model.trim())
    );
    return [...new Set(allModels)].sort();
  }, [data]);
  const uniquePartNumbers = useMemo(() =>
    [...new Set(data.map(item => item.partNumber))].sort(), [data]);
  const uniqueImdsIds = useMemo(() =>
    [...new Set(data.map(item => item.imdsId).filter(id => id))].sort(), [data]);

  // Filter and sort data
  const filteredData = useMemo(() => {
    let filtered = data.filter(item => {
      const matchesSearch = Object.values(item).some(value => {
        if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
          return Object.values(value).some(v => v.toString().toLowerCase().includes(searchTerm.toLowerCase()));
        }
        return value.toString().toLowerCase().includes(searchTerm.toLowerCase());
      });

      const matchesSupplier = !supplierFilter || item.supplier === supplierFilter;
      const matchesVendorCode = !vendorCodeFilter || item.vendorCode === vendorCodeFilter;
      const matchesModelName = !modelNameFilter || item.modelName.includes(modelNameFilter);
      const matchesPartNumber = !partNumberFilter || item.partNumber === partNumberFilter;
      const matchesImdsId = !imdsIdFilter || item.imdsId === imdsIdFilter;

      return matchesSearch && matchesSupplier && matchesVendorCode &&
             matchesModelName && matchesPartNumber && matchesImdsId;
    });

    // Apply date sorting
    if (dateSortDirection) {
      filtered.sort((a, b) => {
        const aTime = a.lastUpdated.getTime();
        const bTime = b.lastUpdated.getTime();
        return dateSortDirection === 'asc' ? aTime - bTime : bTime - aTime;
      });
    }

    return filtered;
  }, [data, searchTerm, supplierFilter, vendorCodeFilter, modelNameFilter,
      partNumberFilter, imdsIdFilter, dateSortDirection]);

  // Use all filtered data (no pagination)
  const displayData = filteredData;

  // Date sorting handler
  const handleDateSort = () => {
    if (dateSortDirection === null) {
      setDateSortDirection('desc');
    } else if (dateSortDirection === 'desc') {
      setDateSortDirection('asc');
    } else {
      setDateSortDirection(null);
    }
  };

  // Get sort icon for date column
  const getDateSortIcon = () => {
    if (dateSortDirection === null) return <ArrowUpDown className="h-4 w-4" />;
    if (dateSortDirection === 'asc') return <ArrowUp className="h-4 w-4" />;
    return <ArrowDown className="h-4 w-4" />;
  };



  return (
    <div className="space-y-6 w-full">
      {/* Header with Title and Controls */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Supplier LCA Data Collection Status</h2>
              <p className="text-sm text-gray-600 mt-1">Eum iusto architecto minus rerum.</p>
            </div>
            <div className="flex gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Button variant="outline" onClick={onExport} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Report
              </Button>
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="text-left p-4 font-medium text-gray-700 w-[11%]">
                  <div className="flex items-center gap-2">
                    Supplier
                    <div className="relative">
                      <select
                        value={supplierFilter}
                        onChange={(e) => setSupplierFilter(e.target.value)}
                        className="appearance-none bg-transparent border-0 text-xs cursor-pointer focus:outline-none"
                        title="Filter by Supplier"
                      >
                        <option value="">All</option>
                        {uniqueSuppliers.map(supplier => (
                          <option key={supplier} value={supplier}>{supplier}</option>
                        ))}
                      </select>
                      <Filter className="h-3 w-3 text-gray-500 pointer-events-none" />
                    </div>
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[9%]">
                  <div className="flex items-center gap-2">
                    Vendor code
                    <div className="relative">
                      <select
                        value={vendorCodeFilter}
                        onChange={(e) => setVendorCodeFilter(e.target.value)}
                        className="appearance-none bg-transparent border-0 text-xs cursor-pointer focus:outline-none"
                        title="Filter by Vendor Code"
                      >
                        <option value="">All</option>
                        {uniqueVendorCodes.map(code => (
                          <option key={code} value={code}>{code}</option>
                        ))}
                      </select>
                      <Filter className="h-3 w-3 text-gray-500 pointer-events-none" />
                    </div>
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[10%]">
                  <div className="flex items-center gap-2">
                    Model Name
                    <div className="relative">
                      <select
                        value={modelNameFilter}
                        onChange={(e) => setModelNameFilter(e.target.value)}
                        className="appearance-none bg-transparent border-0 text-xs cursor-pointer focus:outline-none"
                        title="Filter by Model Name"
                      >
                        <option value="">All</option>
                        {uniqueModelNames.map(model => (
                          <option key={model} value={model}>{model}</option>
                        ))}
                      </select>
                      <Filter className="h-3 w-3 text-gray-500 pointer-events-none" />
                    </div>
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[9%]">
                  <div className="flex items-center gap-2">
                    Part number
                    <div className="relative">
                      <select
                        value={partNumberFilter}
                        onChange={(e) => setPartNumberFilter(e.target.value)}
                        className="appearance-none bg-transparent border-0 text-xs cursor-pointer focus:outline-none"
                        title="Filter by Part Number"
                      >
                        <option value="">All</option>
                        {uniquePartNumbers.map(partNumber => (
                          <option key={partNumber} value={partNumber}>{partNumber}</option>
                        ))}
                      </select>
                      <Filter className="h-3 w-3 text-gray-500 pointer-events-none" />
                    </div>
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[16%]">Description</th>
                <th className="text-left p-4 font-medium text-gray-700 w-[7%]">
                  <div className="flex items-center gap-2">
                    IMDS ID
                    <div className="relative">
                      <select
                        value={imdsIdFilter}
                        onChange={(e) => setImdsIdFilter(e.target.value)}
                        className="appearance-none bg-transparent border-0 text-xs cursor-pointer focus:outline-none"
                        title="Filter by IMDS ID"
                      >
                        <option value="">All</option>
                        {uniqueImdsIds.map(imdsId => (
                          <option key={imdsId} value={imdsId}>{imdsId}</option>
                        ))}
                      </select>
                      <Filter className="h-3 w-3 text-gray-500 pointer-events-none" />
                    </div>
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[11%]">
                  <button
                    onClick={handleDateSort}
                    className="flex items-center gap-2 hover:text-gray-900 transition-colors"
                  >
                    Last updated date
                    {getDateSortIcon()}
                  </button>
                </th>
                <th className="text-center p-4 font-medium text-gray-700 w-[27%]">Data collection status</th>
              </tr>
            </thead>
            <tbody>
              {displayData.map((item) => {
                return (
                  <tr key={item.id} className="border-b hover:bg-gray-50">
                    <td className="p-4 font-medium text-gray-900">{item.supplier}</td>
                    <td className="p-4 text-gray-700">{item.vendorCode}</td>
                    <td className="p-4 text-gray-700 font-medium">{item.modelName}</td>
                    <td className="p-4 text-blue-600 font-medium">{item.partNumber}</td>
                    <td className="p-4 text-gray-700" title={item.description}>
                      {item.description}
                    </td>
                    <td className="p-4 text-gray-700">{item.imdsId || '-'}</td>
                    <td className="p-4 text-gray-700">
                      {item.lastUpdated.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                      })}
                    </td>
                    <td className="p-4">
                      <DataCollectionStatusRow status={item.dataCollectionStatus} />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

    </div>
  );
};