import React, { useState, useMemo, useRef, useEffect } from 'react';
import {
  Search,
  Download,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Filter,
  X,
  Check
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

// Types for our supplier data
export interface DataCollectionCategory {
  process: number; // 0-100 percentage
  energyConsumption: number;
  specialProcessMaterial: number;
  waste: number;
  transportation: number;
}

export interface SupplierData {
  id: string;
  supplier: string;
  vendorCode: string;
  modelName: string;
  partNumber: string;
  description: string;
  imdsId: string;
  lastUpdated: Date;
  dataCollectionStatus: DataCollectionCategory;
}

interface SupplierDataTableProps {
  data: SupplierData[];
  onExport?: () => void;
}



// Component for displaying the data collection status in a single row
const DataCollectionStatusRow: React.FC<{ status: DataCollectionCategory }> = ({ status }) => {
  const categories = [
    { key: 'process', label: 'Process', value: status.process },
    { key: 'energyConsumption', label: 'Energy consumption', value: status.energyConsumption },
    { key: 'specialProcessMaterial', label: 'Social Process Material', value: status.specialProcessMaterial },
    { key: 'waste', label: 'Waste', value: status.waste },
    { key: 'transportation', label: 'Transportation', value: status.transportation },
  ];

  const getProgressColor = (value: number) => {
    if (value >= 90) return 'bg-green-500';
    if (value >= 50) return 'bg-yellow-500';
    if (value > 0) return 'bg-orange-500';
    return 'bg-gray-300';
  };

  return (
    <div className="w-full">
      {/* Progress Bars First */}
      <div className="grid grid-cols-5 gap-1 mb-2">
        {categories.map((category) => (
          <div key={category.key} className="px-2">
            <div className="w-full bg-gray-200 rounded h-2">
              <div
                className={`h-full rounded transition-all duration-300 ${getProgressColor(category.value)}`}
                style={{ width: `${category.value}%` }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Category Labels Below Progress Bars */}
      <div className="grid grid-cols-5 gap-1 mb-2">
        {categories.map((category) => (
          <div key={category.key} className="text-center text-xs font-medium text-gray-700 px-2">
            {category.label}
          </div>
        ))}
      </div>

      {/* Download Icons at Bottom */}
      <div className="grid grid-cols-5 gap-1">
        {categories.map((category) => (
          <div key={category.key} className="flex justify-center px-2">
            {category.value >= 90 ? (
              <Download className="h-4 w-4 text-gray-600 cursor-pointer hover:text-gray-800" />
            ) : (
              <div className="h-4 w-4" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Custom Filter Dropdown Component
const FilterDropdown: React.FC<{
  label: string;
  value: string;
  options: string[];
  onChange: (value: string) => void;
  placeholder?: string;
}> = ({ label, value, options, onChange, placeholder = "All" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm(''); // Clear search when closing
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  // Filter options based on search term
  const filteredOptions = useMemo(() => {
    if (!searchTerm) return options;
    return options.filter(option =>
      option.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [options, searchTerm]);

  const hasActiveFilter = value !== '';
  const displayValue = value || placeholder;

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 ${
          hasActiveFilter
            ? 'bg-blue-100 text-blue-700 border border-blue-200'
            : 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200'
        }`}
        title={`Filter by ${label}`}
      >
        <Filter className="h-3 w-3" />
        {hasActiveFilter && (
          <span className="max-w-20 truncate">{displayValue}</span>
        )}
        <ChevronDown className={`h-3 w-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden">
          <div className="p-3 border-b border-gray-100">
            <div className="text-xs font-medium text-gray-700 mb-2">Filter by {label}</div>

            {/* Search Input */}
            <div className="relative mb-2">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={`Search ${label.toLowerCase()}s...`}
                className="w-full pl-7 pr-3 py-1.5 text-xs border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </div>

            {hasActiveFilter && (
              <button
                onClick={() => {
                  onChange('');
                  setIsOpen(false);
                  setSearchTerm('');
                }}
                className="flex items-center gap-1 text-xs text-red-600 hover:text-red-700 transition-colors"
              >
                <X className="h-3 w-3" />
                Clear filter
              </button>
            )}
          </div>

          <div className="max-h-52 overflow-y-auto">
            {/* All option */}
            <button
              onClick={() => {
                onChange('');
                setIsOpen(false);
                setSearchTerm('');
              }}
              className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors flex items-center justify-between ${
                !hasActiveFilter ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
              }`}
            >
              <span>All {label}s</span>
              {!hasActiveFilter && <Check className="h-4 w-4" />}
            </button>

            {/* Filtered options */}
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <button
                  key={option}
                  onClick={() => {
                    onChange(option);
                    setIsOpen(false);
                    setSearchTerm('');
                  }}
                  className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors flex items-center justify-between ${
                    value === option ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                  }`}
                >
                  <span className="truncate">{option}</span>
                  {value === option && <Check className="h-4 w-4" />}
                </button>
              ))
            ) : (
              <div className="px-3 py-4 text-sm text-gray-500 text-center">
                No {label.toLowerCase()}s found matching "{searchTerm}"
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export const SupplierDataTable: React.FC<SupplierDataTableProps> = ({
  data,
  onExport
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [supplierFilter, setSupplierFilter] = useState('');
  const [vendorCodeFilter, setVendorCodeFilter] = useState('');
  const [modelNameFilter, setModelNameFilter] = useState('');
  const [partNumberFilter, setPartNumberFilter] = useState('');
  const [imdsIdFilter, setImdsIdFilter] = useState('');
  const [dateSortDirection, setDateSortDirection] = useState<'asc' | 'desc' | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Get unique values for dropdown filters
  const uniqueSuppliers = useMemo(() =>
    [...new Set(data.map(item => item.supplier))].sort(), [data]);
  const uniqueVendorCodes = useMemo(() =>
    [...new Set(data.map(item => item.vendorCode))].sort(), [data]);
  const uniqueModelNames = useMemo(() => {
    const allModels = data.flatMap(item =>
      item.modelName.split(',').map(model => model.trim())
    );
    return [...new Set(allModels)].sort();
  }, [data]);
  const uniquePartNumbers = useMemo(() =>
    [...new Set(data.map(item => item.partNumber))].sort(), [data]);
  const uniqueImdsIds = useMemo(() =>
    [...new Set(data.map(item => item.imdsId).filter(id => id))].sort(), [data]);

  // Filter and sort data
  const filteredData = useMemo(() => {
    let filtered = data.filter(item => {
      const matchesSearch = Object.values(item).some(value => {
        if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
          return Object.values(value).some(v => v.toString().toLowerCase().includes(searchTerm.toLowerCase()));
        }
        return value.toString().toLowerCase().includes(searchTerm.toLowerCase());
      });

      const matchesSupplier = !supplierFilter || item.supplier === supplierFilter;
      const matchesVendorCode = !vendorCodeFilter || item.vendorCode === vendorCodeFilter;
      const matchesModelName = !modelNameFilter || item.modelName.includes(modelNameFilter);
      const matchesPartNumber = !partNumberFilter || item.partNumber === partNumberFilter;
      const matchesImdsId = !imdsIdFilter || item.imdsId === imdsIdFilter;

      return matchesSearch && matchesSupplier && matchesVendorCode &&
             matchesModelName && matchesPartNumber && matchesImdsId;
    });

    // Apply date sorting
    if (dateSortDirection) {
      filtered.sort((a, b) => {
        const aTime = a.lastUpdated.getTime();
        const bTime = b.lastUpdated.getTime();
        return dateSortDirection === 'asc' ? aTime - bTime : bTime - aTime;
      });
    }

    return filtered;
  }, [data, searchTerm, supplierFilter, vendorCodeFilter, modelNameFilter,
      partNumberFilter, imdsIdFilter, dateSortDirection]);

  // Pagination calculations
  const totalItems = filteredData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const displayData = filteredData.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, supplierFilter, vendorCodeFilter, modelNameFilter, partNumberFilter, imdsIdFilter, dateSortDirection]);

  // Date sorting handler
  const handleDateSort = () => {
    if (dateSortDirection === null) {
      setDateSortDirection('desc');
    } else if (dateSortDirection === 'desc') {
      setDateSortDirection('asc');
    } else {
      setDateSortDirection(null);
    }
  };

  // Get sort icon for date column
  const getDateSortIcon = () => {
    if (dateSortDirection === null) return <ArrowUpDown className="h-4 w-4" />;
    if (dateSortDirection === 'asc') return <ArrowUp className="h-4 w-4" />;
    return <ArrowDown className="h-4 w-4" />;
  };

  // Get active filters for display
  const activeFilters = useMemo(() => {
    const filters = [];
    if (supplierFilter) filters.push({ label: 'Supplier', value: supplierFilter, clear: () => setSupplierFilter('') });
    if (vendorCodeFilter) filters.push({ label: 'Vendor Code', value: vendorCodeFilter, clear: () => setVendorCodeFilter('') });
    if (modelNameFilter) filters.push({ label: 'Model', value: modelNameFilter, clear: () => setModelNameFilter('') });
    if (partNumberFilter) filters.push({ label: 'Part Number', value: partNumberFilter, clear: () => setPartNumberFilter('') });
    if (imdsIdFilter) filters.push({ label: 'IMDS ID', value: imdsIdFilter, clear: () => setImdsIdFilter('') });
    return filters;
  }, [supplierFilter, vendorCodeFilter, modelNameFilter, partNumberFilter, imdsIdFilter]);

  const clearAllFilters = () => {
    setSupplierFilter('');
    setVendorCodeFilter('');
    setModelNameFilter('');
    setPartNumberFilter('');
    setImdsIdFilter('');
    setDateSortDirection(null);
    setCurrentPage(1);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  const goToPreviousPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };



  return (
    <div className="space-y-6 w-full">
      {/* Header with Title and Controls */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Supplier LCA Data Collection Status</h2>
              <p className="text-sm text-gray-600 mt-1">Eum iusto architecto minus rerum.</p>
            </div>
            <div className="flex gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Button variant="outline" onClick={onExport} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Report
              </Button>
            </div>
          </div>
        </div>

        {/* Active Filters */}
        {(activeFilters.length > 0 || dateSortDirection) && (
          <div className="px-6 py-3 bg-blue-50 border-b border-blue-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-wrap">
                <span className="text-sm font-medium text-blue-700">Active filters:</span>
                {activeFilters.map((filter, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium"
                  >
                    {filter.label}: {filter.value}
                    <button
                      onClick={filter.clear}
                      className="hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
                {dateSortDirection && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                    Date: {dateSortDirection === 'asc' ? 'Oldest first' : 'Newest first'}
                    <button
                      onClick={() => setDateSortDirection(null)}
                      className="hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
              </div>
              <button
                onClick={clearAllFilters}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
              >
                Clear all
              </button>
            </div>
          </div>
        )}

        {/* Results Summary */}
        <div className="px-6 py-2 bg-gray-50 border-b text-sm text-gray-600">
          Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} suppliers
          {totalItems !== data.length && (
            <span className="text-blue-600 font-medium"> (filtered from {data.length} total)</span>
          )}
        </div>

        {/* Table */}
        <div className="overflow-x-auto border border-gray-200 rounded-lg">
          <table className="w-full border-collapse">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="text-left p-4 font-medium text-gray-700 w-[11%] border-r border-gray-200">
                  <div className="flex items-center gap-3">
                    <span>Supplier</span>
                    <FilterDropdown
                      label="Supplier"
                      value={supplierFilter}
                      options={uniqueSuppliers}
                      onChange={setSupplierFilter}
                    />
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[9%] border-r border-gray-200">
                  <div className="flex items-center gap-3">
                    <span>Vendor code</span>
                    <FilterDropdown
                      label="Vendor Code"
                      value={vendorCodeFilter}
                      options={uniqueVendorCodes}
                      onChange={setVendorCodeFilter}
                    />
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[10%] border-r border-gray-200">
                  <div className="flex items-center gap-3">
                    <span>Model Name</span>
                    <FilterDropdown
                      label="Model"
                      value={modelNameFilter}
                      options={uniqueModelNames}
                      onChange={setModelNameFilter}
                    />
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[9%] border-r border-gray-200">
                  <div className="flex items-center gap-3">
                    <span>Part number</span>
                    <FilterDropdown
                      label="Part Number"
                      value={partNumberFilter}
                      options={uniquePartNumbers}
                      onChange={setPartNumberFilter}
                    />
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[16%] border-r border-gray-200">Description</th>
                <th className="text-left p-4 font-medium text-gray-700 w-[7%] border-r border-gray-200">
                  <div className="flex items-center gap-3">
                    <span>IMDS ID</span>
                    <FilterDropdown
                      label="IMDS ID"
                      value={imdsIdFilter}
                      options={uniqueImdsIds}
                      onChange={setImdsIdFilter}
                    />
                  </div>
                </th>
                <th className="text-left p-4 font-medium text-gray-700 w-[11%] border-r border-gray-200">
                  <button
                    onClick={handleDateSort}
                    className="flex items-center gap-2 hover:text-gray-900 transition-colors"
                  >
                    Last updated date
                    {getDateSortIcon()}
                  </button>
                </th>
                <th className="text-center p-4 font-medium text-gray-700 w-[27%]">Data collection status</th>
              </tr>
            </thead>
            <tbody>
              {displayData.map((item) => {
                return (
                  <tr key={item.id} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="p-4 font-medium text-gray-900 border-r border-gray-200">{item.supplier}</td>
                    <td className="p-4 text-gray-700 border-r border-gray-200">{item.vendorCode}</td>
                    <td className="p-4 text-gray-700 font-medium border-r border-gray-200">{item.modelName}</td>
                    <td className="p-4 text-blue-600 font-medium border-r border-gray-200">{item.partNumber}</td>
                    <td className="p-4 text-gray-700 border-r border-gray-200" title={item.description}>
                      {item.description}
                    </td>
                    <td className="p-4 text-gray-700 border-r border-gray-200">{item.imdsId || '-'}</td>
                    <td className="p-4 text-gray-700 border-r border-gray-200">
                      {item.lastUpdated.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                      })}
                    </td>
                    <td className="p-4">
                      <DataCollectionStatusRow status={item.dataCollectionStatus} />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 bg-white border-t border-gray-200 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-700">Items per page:</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                  className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                </select>
              </div>
              <div className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
                className="flex items-center gap-1 px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else if (currentPage <= 3) {
                    pageNumber = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + i;
                  } else {
                    pageNumber = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`px-3 py-1 text-sm border rounded transition-colors ${
                        currentPage === pageNumber
                          ? 'bg-blue-500 text-white border-blue-500'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1 px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </div>

    </div>
  );
};