import React, { useState, useMemo } from 'react';
import {
  Search,
  Download
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

// Types for our supplier data
export interface DataCollectionCategory {
  process: number; // 0-100 percentage
  energyConsumption: number;
  specialProcessMaterial: number;
  waste: number;
  transportation: number;
}

export interface SupplierData {
  id: string;
  supplier: string;
  vendorCode: string;
  partNumber: string;
  description: string;
  imdsId: string;
  lastUpdated: Date;
  dataCollectionStatus: DataCollectionCategory;
}

interface SupplierDataTableProps {
  data: SupplierData[];
  onExport?: () => void;
}



// Component for displaying individual category status with progress bar and download icon
const CategoryStatusCell: React.FC<{
  label: string;
  value: number;
  isCompleted: boolean;
}> = ({ label, value, isCompleted }) => {
  const getProgressColor = () => {
    if (value >= 90) return 'bg-green-500';
    if (value >= 50) return 'bg-yellow-500';
    if (value > 0) return 'bg-orange-500';
    return 'bg-gray-300';
  };

  return (
    <div className="text-center p-2">
      <div className="text-xs font-medium text-gray-700 mb-2">{label}</div>
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div
          className={`h-full rounded-full transition-all duration-300 ${getProgressColor()}`}
          style={{ width: `${value}%` }}
        />
      </div>
      <div className="flex justify-center">
        {isCompleted ? (
          <Download className="h-4 w-4 text-gray-600 cursor-pointer hover:text-gray-800" />
        ) : (
          <div className="h-4 w-4" />
        )}
      </div>
    </div>
  );
};

export const SupplierDataTable: React.FC<SupplierDataTableProps> = ({
  data,
  onExport
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Filter data
  const filteredData = useMemo(() => {
    return data.filter(item => {
      const matchesSearch = Object.values(item).some(value => {
        if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
          return Object.values(value).some(v => v.toString().toLowerCase().includes(searchTerm.toLowerCase()));
        }
        return value.toString().toLowerCase().includes(searchTerm.toLowerCase());
      });

      return matchesSearch;
    });
  }, [data, searchTerm]);

  // Use all filtered data (no pagination)
  const displayData = filteredData;



  return (
    <div className="space-y-6 w-full">
      {/* Header with Title and Controls */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Supplier LCA Data Collection Status</h2>
              <p className="text-sm text-gray-600 mt-1">Eum iusto architecto minus rerum.</p>
            </div>
            <div className="flex gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Button variant="outline" onClick={onExport} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Report
              </Button>
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="text-left p-4 font-medium text-gray-700 w-[12%]">Supplier</th>
                <th className="text-left p-4 font-medium text-gray-700 w-[10%]">Vendor code</th>
                <th className="text-left p-4 font-medium text-gray-700 w-[10%]">Part number</th>
                <th className="text-left p-4 font-medium text-gray-700 w-[18%]">Description</th>
                <th className="text-left p-4 font-medium text-gray-700 w-[8%]">IMDS ID</th>
                <th className="text-left p-4 font-medium text-gray-700 w-[12%]">Last updated date</th>
                <th className="text-center p-4 font-medium text-gray-700 w-[30%]">Data collection status</th>
              </tr>
            </thead>
            <tbody>
              {displayData.map((item) => {
                const categories = [
                  { key: 'process', label: 'Process', value: item.dataCollectionStatus.process },
                  { key: 'energyConsumption', label: 'Energy consumption', value: item.dataCollectionStatus.energyConsumption },
                  { key: 'specialProcessMaterial', label: 'Social Process Material', value: item.dataCollectionStatus.specialProcessMaterial },
                  { key: 'waste', label: 'Waste', value: item.dataCollectionStatus.waste },
                  { key: 'transportation', label: 'Transportation', value: item.dataCollectionStatus.transportation },
                ];

                return (
                  <tr key={item.id} className="border-b hover:bg-gray-50">
                    <td className="p-4 font-medium text-gray-900">{item.supplier}</td>
                    <td className="p-4 text-gray-700">{item.vendorCode}</td>
                    <td className="p-4 text-blue-600 font-medium">{item.partNumber}</td>
                    <td className="p-4 text-gray-700" title={item.description}>
                      {item.description}
                    </td>
                    <td className="p-4 text-gray-700">{item.imdsId || '-'}</td>
                    <td className="p-4 text-gray-700">
                      {item.lastUpdated.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                      })}
                    </td>
                    <td className="p-4">
                      <div className="grid grid-cols-5 gap-2">
                        {categories.map((category) => (
                          <CategoryStatusCell
                            key={category.key}
                            label={category.label}
                            value={category.value}
                            isCompleted={category.value >= 90}
                          />
                        ))}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

    </div>
  );
};